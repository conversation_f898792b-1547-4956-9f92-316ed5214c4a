{"基础检查": [{"name": "检查项目1", "icon": "../image/OTA1.png", "path": "../script/status_push_deploy_script.py", "description": "通过OTA技术、安装传感器状态查询助手并配置环境变量"}, {"name": "检查项目2", "icon": "../image/OTA2.png", "path": "../script/lidar_push_deploy_script.py", "description": "通过OTA技术、安装激光雷达自动标定并配置环境变量"}, {"name": "检查项目3", "icon": "../image/OTA3.png", "path": "../script/camera_push_deploy_script.py", "description": "通过OTA技术、安装联合标定自动标定并配置环境变量"}, {"name": "检查项目4", "icon": "../image/window-icon.png", "path": "../exe/NoMachine/bin/nxplayer.exe", "description": "远程桌面连接工具，用于远程连接到其他电脑"}], "安装工具": [{"name": "传感器状态一键部署程序", "icon": "../image/OTA1.png", "path": "../script/status_push_deploy_script.py", "description": "通过OTA技术、安装传感器状态查询助手并配置环境变量"}, {"name": "激光雷达一键部署程序", "icon": "../image/OTA2.png", "path": "../script/lidar_push_deploy_script.py", "description": "通过OTA技术、安装激光雷达自动标定并配置环境变量"}, {"name": "联合标定一键部署程序", "icon": "../image/OTA3.png", "path": "../script/camera_push_deploy_script.py", "description": "通过OTA技术、安装联合标定自动标定并配置环境变量"}, {"name": "NoMachine", "icon": "../image/window-icon.png", "path": "../exe/NoMachine/bin/nxplayer.exe", "description": "远程桌面连接工具，用于远程连接到其他电脑"}], "调试工具": [{"name": "串口调试助手", "icon": "../image/CKTS.png", "path": "../exe/CKTS.exe", "description": "用于串口通信的调试工具，支持各种波特率、数据位、校验方式等参数设置，可实时监控和发送串口数据"}, {"name": "激光雷达上位机", "icon": "../image/leishen.png", "path": "../exe/LS32/LSC32_1212byte_v3.0.3_201116.exe", "description": "雷神激光雷达专用上位机软件，用于激光雷达的实时数据采集、可视化显示和参数配置"}, {"name": "大陆雷达调试工具", "icon": "../image/Radar_Monitor.png", "path": "../exe/Radar_Monitor_Release_V2.2/Radar_Monitor.exe", "description": "大陆毫米波雷达调试专用工具，用于雷达数据实时监控、参数配置和性能分析"}, {"name": "USB_CAN_Tool", "icon": "../image/USB_CAN_Tool.png", "path": "../exe/USB_CAN TOOL/USB_CAN_Tool.exe", "description": "用于USB转CAN设备的配置和CAN总线数据监控工具，支持CAN消息的发送、接收和分析"}, {"name": "组合惯导调试工具", "icon": "../image/RTKQC.png", "path": "../exe/RTKQC/RTKQC.exe", "description": "用于组合惯性导航系统的调试和数据分析，支持GNSS/INS数据的实时显示和记录"}, {"name": "GCANTools", "path": "../exe/GCANTools/GCANTools.exe", "icon": "../image/GCANTools.png", "description": "广成科技CAN总线调试工具，支持CAN/CANFD通信配置、监控和数据分析", "type": "可执行文件"}, {"name": "CAN中继", "path": "../exe/USB_CAN TOOL/CANBridgeConfig.exe", "icon": "../image/CANzj.png", "description": "该工具是一个用于配置CAN中继器的软件界面，核心功能是通过参数设置实现两个CAN通道间的数据中继，适用于工业自动化、汽车电子等领域中需要扩展、隔离或调试CAN网络的场景。", "type": "可执行文件"}, {"name": "波特率计算工具", "path": "../exe/USB_CAN TOOL/波特率计算工具.exe", "icon": "../image/btljs.png", "description": "此工具是面向Philips CAN控制器的参数配置助手，核心功能是通过自动计算和验证BTR0/BTR1值，实现精确的波特率设置与总线时序优化，适用于汽车电子、工业控制等领域的CAN通信开发与调试。", "type": "可执行文件"}], "测试工具": [{"name": "wireshark网络抓包工具", "icon": "../image/wireshark.png", "path": "../exe/Wireshark/Wireshark.exe", "description": "专业的网络数据包分析工具，用于捕获和分析车载以太网、UDP、TCP等网络协议数据，支持深度包检查和协议解析"}, {"name": "CAN分析仪自检工具", "path": "../exe/USB_CAN TOOL/USBCAN Test.exe", "icon": "../image/CANzj.png", "description": "此工具是一款针对USB-CAN适配器的测试与监控软件，核心功能是确保硬件正确连接，并实时捕获、解析CAN总线数据，适用于工业控制、汽车电子等领域的设备调试与故障排查。", "type": "可执行文件"}, {"name": "RTKPLOT ver.2.4.3 b8", "path": "../exe/RTKQC/rtkplot.exe", "icon": "../image/RTKPLOT ver.2.4.3 b8.png", "description": "RTKPLOT ver.2.4.3 b8 是一款专用于高精度全球导航卫星系统（GNSS）数据处理与可视化的工具，主要服务于测绘、工程测量及科研领域。", "type": "可执行文件"}, {"name": "语音控制底盘", "path": "../script/control.py", "icon": "../image/control.png", "description": "语音控制底盘", "type": "Python脚本"}], "标定工具": [{"name": "autoware_ai_工具箱", "path": "../script/autoware_ai_utilities-master/runtime_manager/scripts/runtime_manager_dialog.py", "icon": "../image/autoware.png", "description": "基于Autoware.AI的工具集，用于自动驾驶系统的传感器标定、参数配置和运行时管理", "type": "Python脚本"}, {"name": "摄像头标定", "icon": "../image/fish.png", "path": "http://*************:11451/run-script", "description": "专用于鱼眼相机镜头畸变校正和参数标定的工具，支持内外参数计算和图像矫正", "action": "background_request"}, {"name": "激光雷达和相机标定", "path": "../script/lidar_camera_calibration-master/main.py", "icon": "../image/lidar_camera_calibration-master.png", "description": "用于计算和优化激光雷达与相机之间的外参矩阵，实现点云数据与图像数据的精确融合", "type": "Python脚本"}, {"name": "激光雷达自动标定", "path": "../script/lidarcor.py", "icon": "../image/lidar_automatic_calibration.png", "description": "用于激光雷达自动标定，使用方法：点击“开始标定”即可自动完成激光雷达标定", "type": "Python脚本"}, {"name": "自动联合标定", "path": "../script/cameracor.py", "icon": "../image/unite.png", "description": "用于自动联合标定，使用方法：点击“开始标定”即可自动完成联合标定的标定", "type": "Python脚本"}], "便捷工具": {"计算工具": [{"name": "进制计算器", "icon": "../image/calc.png", "path": "../script/calc.py", "description": "支持二进制、八进制、十进制、十六进制等各种进制间转换的计算工具，具有友好的图形界面"}, {"name": "SA计算器", "path": "../exe/ZXDoc/SATool.exe", "icon": "../image/SA.png", "description": "汽车电子安全访问(Security Access)计算工具，用于ECU刷写和参数修改的安全验证，防止未授权访问", "type": "可执行文件"}, {"name": "欧拉角转四元数工具", "icon": "../image/quaternion.png", "path": "../script/quaternion.py", "description": "姿态表示转换工具，支持欧拉角、四元数、旋转矩阵等不同姿态表示方式之间的互相转换"}, {"name": "基于GPS和NDT转欧拉角", "icon": "../image/yaw,pitch,roll.png", "path": "../script/yaw,pitch,roll.py", "description": "利用GPS数据和NDT点云匹配结果计算车辆姿态欧拉角的工具，用于自动驾驶定位和姿态估计"}, {"name": "文本转十六进制", "path": "../exe/ZXDoc/CharHexTool.exe", "icon": "../image/转换.png", "description": "文本与十六进制数据互相转换的工具，支持ASCII、UTF-8等编码格式，方便进行数据格式转换和查看", "type": "可执行文件"}, {"name": "毫米波雷达原始数据解析器", "icon": "../image/radar.png", "path": "../script/radar.py", "description": "用于解析和可视化毫米波雷达原始数据的工具，支持数据格式转换、波形分析和目标识别"}, {"name": "激光雷达数据包解析器", "icon": "../image/lidars.png", "path": "../script/lidars.py", "description": "解析激光雷达输出的原始点云坐标数据，支持多种格式转换、数据过滤和坐标变换功能"}, {"name": "校验码计算工具", "path": "../exe/ZXDoc/CheckCodeTool.exe", "icon": "../image/KHCFDC_计算器.png", "description": "支持CRC32、CRC16、CRC8、MD5等多种校验算法的计算工具，用于数据完整性验证和通信协议设计", "type": "可执行文件"}, {"name": "波特率计算器", "path": "../exe/ZXDoc/ZBaudcalTool.exe", "icon": "../image/icon_波特率.png", "description": "用于计算和配置各种通信设备波特率的工具，支持常见串行通信接口参数计算和误差分析", "type": "可执行文件"}], "其他工具": [{"name": "SSH连接工具", "icon": "../image/ssh.png", "path": "../script/ssh.py", "description": "轻量级SSH客户端工具，支持远程服务器连接、认证和命令执行，可保存常用连接配置"}, {"name": "激光雷达pack包直接解析器", "icon": "../image/lidar.png", "path": "../script/lidar.py", "description": "用于解析激光雷达打包数据和直接数据流的工具，支持数据提取、格式转换和点云可视化"}, {"name": "图片爬虫", "icon": "../image/tppc.png", "path": "../script/tppc.py", "description": "网络图片批量下载工具，支持多种搜索引擎和网站的图片资源爬取，可自定义筛选条件和保存路径", "type": "Python脚本"}, {"name": "rufus", "path": "../exe/rufus/rufus-4.3.exe", "icon": "../image/rufus.png", "description": "电脑系统U盘启动盘制作工具", "type": "可执行文件"}, {"name": "驱动精灵", "path": "../exe/驱动精灵/ChipGenius_v4_20_1107.exe", "icon": "../image/qdjl.png", "description": "驱动精灵", "type": "可执行文件"}, {"name": "主控查询", "path": "../exe/驱动精灵/FlashMaster.exe", "icon": "../image/zkcx.png", "description": "主控查询", "type": "可执行文件"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "path": "../exe/DiskGenius/DiskGenius.exe", "icon": "../image/Diskgenius.png", "description": "Diskgenius是一款电脑硬盘修复工具", "type": "可执行文件"}, {"name": "文件转换", "path": "../exe/ZXDoc/ConvertTool.exe", "icon": "../image/04转换.png", "description": "MF4 文件中的同类信号合并工具，支持多种文件格式之间的转换，如MDF、CSV、TXT等，用于数据分析和处理", "type": "可执行文件"}, {"name": "标定软件", "path": "../exe/ZXDoc/ZCDTool.exe", "icon": "../image/标定.png", "description": "ECU开发调试过程中的参数标定工具，支持实时参数修改、数据记录和分析，适用于汽车电子系统开发和标定", "type": "可执行文件"}, {"name": "网络设备配置", "path": "../exe/ZXDoc/CANetConfigurer.exe", "icon": "../image/网络设备配置.png", "description": "用于CAN/CANFD网络设备参数配置的专业工具，支持多种CAN设备的发现、配置和管理", "type": "可执行文件"}, {"name": "科学上网", "icon": "../image/clash.png", "path": "../script/clash.py", "description": "Windows系统代理设置工具，可一键开启或关闭系统代理，支持自定义代理服务器和端口配置"}, {"name": "ZXDoc", "path": "../exe/ZXDoc/ZXDoc.exe", "icon": "../image/ZXDoc.jpg", "description": "为汽车电子开发、测试和诊断提供了一个集成化的平台，可以帮助工程师进行：1.汽车网络通信测试、2.诊断数据分析、3.标定参数调整、4.数据采集和监控、5.系统仿真验证", "type": "可执行文件"}, {"name": "TCP&UDP调试工具", "path": "../exe/ZCANPRO/TCP&UDPDebug/TCPUDPDbg.exe", "icon": "../image/tcp.png", "description": "TCP/UDP数据的收发功能\nTCP模式支持服务器和客户端模式\n支持多连接,可以同时对多路连接进行操作\nUDP模式支持组播方式\n可以显示当前数据传输速度\n数据显示方式可以为ASCII和HEX两种\n可以发送文件\n可以把接收到的数据实时保存到文件", "type": "可执行文件"}, {"name": "激光雷达点云查看工具", "path": "../script/lidar_tools.py", "icon": "../image/lidar_tools.png", "description": "激光雷达点云查看工具", "type": "Python脚本"}, {"name": "工具箱自检", "path": "../script/examine.py", "icon": "../image/examine.png", "description": "工具箱自检工具", "type": "Python脚本"}]}, "信息查询": {"本地查询": [{"name": "传感器标准资料", "path": "../book/data/data1", "icon": "../image/cgq.png", "description": "包含国家安全标准和行业传感器的标准文档，为智能网联汽车开发技术规范参考", "type": "资料"}, {"name": "智能网联汽车国家标准资料", "path": "../book/data/data2", "icon": "../image/read.png", "description": "包含智能网联汽车标准文档和相关政策法规，为智能网联汽车开发提供法规依据和技术规范参考", "type": "资料"}, {"name": "深蓝SL03资料", "path": "../book/data/data3", "icon": "../image/shenlan.png", "description": "深蓝SL03车型的技术规格、系统架构、接口定义和测试方法等全面技术文档", "type": "资料"}, {"name": "摄像头装调测标资料", "path": "../book/data/data8", "icon": "../image/传感器.png", "description": "摄像头装配、调试、测试和标定的完整技术资料，提供从安装到性能优化的全流程指导文档", "type": "可执行文件"}, {"name": "毫米波雷达装调测标资料", "path": "../book/data/data4", "icon": "../image/传感器.png", "description": "毫米波雷达全生命周期技术文档，包括装配指南、调试方法、测试规范和标定流程，确保雷达系统性能最优化", "type": "可执行文件"}, {"name": "激光雷达装调测标资料", "path": "../book/data/data5", "icon": "../image/传感器.png", "description": "激光雷达系统装配、调试、测试和标定的完整技术资料，提供从安装到性能优化的全流程指导文档", "type": "资料"}, {"name": "组合惯导装调测标资料", "path": "../book/data/data6", "icon": "../image/传感器.png", "description": "组合惯性导航系统技术资料，包含使用手册、卫星导航增强与惯导组合定位技术文档及CHC CGI-430厘米级组合导航系统说明", "type": "可执行文件"}, {"name": "智能网联汽车资料", "path": "../book/data/data7", "icon": "../image/汽车.png", "description": "涵盖智能网联汽车基础知识、通信技术、自动驾驶、组合导航、信息安全及软件要求等全面技术资料，包含测试道路规范", "type": "可执行文件"}, {"name": "DBC文件查看工具", "icon": "../image/CAN_DBC.png", "path": "../exe/DBC/candb/Exec32/candb.exe", "description": "CAN总线数据库(DBC文件)查看和编辑工具，支持信号定义、消息解析和参数配置，是CAN协议开发的必备工具"}, {"name": "DBC比较", "path": "../exe/ZXDoc/DBCCompare.exe", "icon": "../image/串并,比对,比较.png", "description": "DBC文件比较和分析工具，可按ID或其他属性排序，支持不同版本DBC文件的差异对比和合并", "type": "可执行文件"}], "在线查询": [{"name": "DEEPSEEK", "icon": "../image/deepseek.png", "path": "https://www.deepseek.com/", "description": "DeepSeek是杭州深度求索人工智能基础技术研究有限公司推出的AI助手，免费体验与全球领先AI模型的互动交流"}, {"name": "KIMI", "icon": "../image/kimi.png", "path": "https://kimi.moonshot.cn/", "description": "Kimi是北京月之暗面科技有限公司于2023年10月9日推出的一款智能助手，主要应用场景为专业学术论文的翻译和理解、辅助分析法律问题、快速理解API开发文档等，是全球首个支持输入20万汉字的智能助手产品。"}, {"name": "通义千问", "icon": "../image/tyqw.png", "path": "https://www.tongyi.com/", "description": "通义千问，阿里巴巴推出的大型语言模型，提供自然语言理解、知识问答和智能内容生成等服务"}, {"name": "秘塔AI", "icon": "../image/mita.png", "path": "https://metaso.cn/", "description": "秘塔AI搜索是秘塔科技旗下的搜索产品，其产品是简单、无广告、直接的搜索答案。"}, {"name": "腾讯元宝", "icon": "../image/yuanbao.png", "path": "https://yuanbao.tencent.com/", "description": "腾讯元宝是深圳市腾讯计算机系统有限公司基于自研混元大模型开发的C端AI助手"}, {"name": "chatgpt", "icon": "../image/chatgpt.png", "path": "https://chatgpt.com/", "description": "OpenAI开发的先进对话式人工智能模型，能够理解和生成自然语言，回答问题并提供各类信息支持"}, {"name": "百度AI", "icon": "../image/baidu.png", "path": "https://chat.baidu.com/", "description": "百度推出的人工智能对话和服务平台，提供智能问答、知识检索和自然语言交互功能"}, {"name": "豆包", "icon": "../image/doubao.png", "path": "https://www.doubao.com/chat/", "description": "字节跳动推出的AI助手，擅长中文对话、知识问答和内容创作，支持多种应用场景"}, {"name": "纳米AI", "icon": "../image/namipng.png", "path": "https://deepseek.n.cn/", "description": "基于DeepSeek模型的智能AI助手，提供多领域知识服务和智能对话功能"}, {"name": "扣子空间AI", "icon": "../image/cozi.png", "path": "https://space.coze.cn/", "description": "字节跳动旗下的AI创作平台，用于构建自定义AI机器人和自动化应用场景"}, {"name": "文心一言", "icon": "../image/yiyan.png", "path": "https://yiyan.baidu.com/", "description": "百度研发的大型语言模型，具备中文理解和生成能力，支持对话、创作和知识检索等功能"}, {"name": "天工AI", "icon": "../image/tiangong.png", "path": "https://www.tiangong.cn/", "description": "昆仑万维推出的AI大模型产品，提供智能对话、知识问答和内容生成服务"}, {"name": "智谱清言", "icon": "../image/zpqy.png", "path": "https://chatglm.cn/main", "description": "基于ChatGLM大模型的智能对话系统，由智谱AI和清华大学合作开发，擅长中文交互"}, {"name": "知乎直搜", "icon": "../image/zihuzhisou.png", "path": "https://zhida.zhihu.com/", "description": "知乎推出的AI搜索和问答工具，结合平台高质量内容与AI能力，提供精准的知识检索服务"}, {"name": "CSDN社区", "icon": "../image/csdn.png", "path": "https://www.csdn.net/", "description": "中国专业IT社区CSDN (Chinese Software Developer Network) 创立于1999年，致力于为中国软件开发者提供知识传播、在线学习、职业发展等全生命周期服务。"}, {"name": "<PERSON><PERSON><PERSON>", "icon": "../image/github.png", "path": "https://github.com/", "description": "GitHub是一项基于云的服务，为软件开发和Git版本控制提供Internet托管。这有助于开发人员存储和管理他们的代码，同时跟踪和控制对其代码的更改。 [21]GitHub拥有超过1.5亿开发者，400万个组织机构，4.2亿个存储库。 [27-29]"}, {"name": "pytorch", "path": "https://pytorch.org/", "icon": "../image/pytorch.png", "description": "PyTorch是Facebook开发的深度学习框架，提供灵活的张量计算和动态神经网络，广泛用于AI研究和应用开发", "type": "网页链接"}, {"name": "全国标准信息公共服务平台", "icon": "../image/std.samr.gov.cn.png", "path": "https://std.samr.gov.cn/gb/", "description": "国家市场监督管理总局标准技术管理司建立的官方平台，提供国家标准、行业标准等权威检索和查询服务"}, {"name": "中国知网", "icon": "../image/ziwang.png", "path": "https://kns.cnki.net/", "description": "中国知网是中国学术期刊、学位论文、会议论文等学术资源的综合检索平台，提供海量学术文献的检索和获取服务"}]}, "项目进度监控": [{"name": "项目进度监控", "icon": "../image/监控.png", "path": "../script/monitor.py", "description": "项目进度监控，用于监控项目进度，并记录项目进度", "type": "Python脚本"}, {"name": "AI故障诊断助手", "icon": "../image/ai_fault.png", "description": "AI智能故障诊断，通过对话形式解决常见技术问题", "action": "chat"}]}